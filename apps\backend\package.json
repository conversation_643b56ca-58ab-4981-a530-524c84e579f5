{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "ts-node src/index.ts", "build": "npm run build:clean && npm run build:ts", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:ts": "tsc", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.2", "dependencies": {"@koa/cors": "^5.0.0", "@koa/router": "^13.1.0", "@prisma/client": "^6.10.1", "fs-extra": "^11.3.0", "koa": "^3.0.0", "koa-body": "^6.0.1", "koa-static": "^5.0.0", "multer": "^2.0.1", "socket.io": "^4.8.1"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/koa": "^2.15.0", "@types/koa__router": "^12.0.4", "@types/multer": "^1.4.13", "@types/node": "^24.0.7", "prisma": "^6.10.1", "ts-node": "^10.9.2", "typescript": "~5.8.3"}}