generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model File {
  id        String   @id @default(uuid())
  name      String
  size      Int
  mimeType  String?
  hash      String   @unique
  chunks    Int
  status    String   @default("uploading")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // chunks Chunk[]
  Chunk Chunk[]
}

model Chunk {
  id        Int      @id @default(autoincrement())
  index     Int
  hash      String
  fileId    String
  file      File     @relation(fields: [fileId], references: [id])
  createdAt DateTime @default(now())

  @@unique([fileId, index])
}
