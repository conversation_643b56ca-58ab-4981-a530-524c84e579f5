{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "outDir": "dist",
    "baseUrl": "./src",
    "types": ["node"],
    "typeRoots": ["node_modules/@types", "./src/types"],
    "paths": {
      "@koa/cors": ["src/types/koa__cors.d.ts"]
    }
    // 移除 paths 配置，typeRoots 已覆盖
  },
  "include": ["src/**/*.ts"],
  "exclude": ["node_modules", "dist"]
}
