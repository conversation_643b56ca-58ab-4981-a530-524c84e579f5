/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BlogMenu: typeof import('./src/components/BlogMenu.vue')['default']
    CanvasTable: typeof import('./src/components/CanvasTable.vue')['default']
    ComputationDemo: typeof import('./src/components/WebWorker/ComputationDemo.vue')['default']
    DashboardHeader: typeof import('./src/components/DataDashboard/DashboardHeader.vue')['default']
    DataFlowAnimation: typeof import('./src/components/DataDashboard/DataFlowAnimation.vue')['default']
    DemoHeader: typeof import('./src/components/WebWorker/DemoHeader.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EnhancedForm: typeof import('./src/components/EnhancedForm/EnhancedForm.vue')['default']
    EnhancedTable: typeof import('./src/components/EnhancedTable/EnhancedTable.vue')['default']
    FieldPropertyEditor: typeof import('./src/components/SmartForm/FieldPropertyEditor.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    GeoDistributionMap: typeof import('./src/components/DataDashboard/GeoDistributionMap.vue')['default']
    GlobalModal: typeof import('./src/components/GlobalModal/GlobalModal.vue')['default']
    GlobalStatus: typeof import('./src/components/OfflineOptimization/GlobalStatus.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    ImageDemo: typeof import('./src/components/WebWorker/ImageDemo.vue')['default']
    IncrementalUpdate: typeof import('./src/components/OfflineOptimization/IncrementalUpdate.vue')['default']
    IndexedDBManager: typeof import('./src/components/OfflineOptimization/IndexedDBManager.vue')['default']
    LunarCalendar: typeof import('./src/components/LunarCalendar.vue')['default']
    MetricsPanel: typeof import('./src/components/DataDashboard/MetricsPanel.vue')['default']
    NetworkAdaptive: typeof import('./src/components/OfflineOptimization/NetworkAdaptive.vue')['default']
    NetworkTopology: typeof import('./src/components/DataDashboard/NetworkTopology.vue')['default']
    OverviewCards: typeof import('./src/components/DataDashboard/OverviewCards.vue')['default']
    PerformanceMonitor: typeof import('./src/components/WebWorker/PerformanceMonitor.vue')['default']
    RealtimeLogs: typeof import('./src/components/DataDashboard/RealtimeLogs.vue')['default']
    RealtimeTrendChart: typeof import('./src/components/DataDashboard/RealtimeTrendChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ServerPerformanceChart: typeof import('./src/components/DataDashboard/ServerPerformanceChart.vue')['default']
    ServiceWorkerManager: typeof import('./src/components/OfflineOptimization/ServiceWorkerManager.vue')['default']
    SharingDemo: typeof import('./src/components/WebWorker/SharingDemo.vue')['default']
    SmartFieldPreview: typeof import('./src/components/SmartForm/SmartFieldPreview.vue')['default']
    SmartFormAI: typeof import('./src/components/SmartForm/SmartFormAI.vue')['default']
    SmartFormAnalyzer: typeof import('./src/components/SmartForm/SmartFormAnalyzer.vue')['default']
    SmartFormDesigner: typeof import('./src/components/SmartForm/SmartFormDesigner.vue')['default']
    SmartFormEngine: typeof import('./src/components/SmartForm/SmartFormEngine.vue')['default']
    SmartFormPreview: typeof import('./src/components/SmartForm/SmartFormPreview.vue')['default']
    SmartFormRenderer: typeof import('./src/components/SmartForm/SmartFormRenderer.vue')['default']
    SystemStatusChart: typeof import('./src/components/DataDashboard/SystemStatusChart.vue')['default']
    VideoDemo: typeof import('./src/components/WebWorker/VideoDemo.vue')['default']
  }
}
