{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "start": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.4.0", "chart.js": "^4.5.0", "cropperjs": "^1.6.2", "d3": "^7.9.0", "element-plus": "^2.10.2", "idb": "^8.0.3", "prismjs": "1.30.0", "socket.io-client": "^4.8.1", "spark-md5": "^3.0.2", "vue": "^3.5.17", "vue-pdf-embed": "^2.1.3", "vue-request": "^2.0.4", "vue-router": "^4.5.1", "vuedraggable": "^2.24.3"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/cropperjs": "^1.3.3", "@types/d3": "^7.4.3", "@types/node": "^24.0.7", "@types/prismjs": "^1.26.5", "@types/spark-md5": "^3.0.5", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "8.4.41", "sass": "^1.89.2", "tailwindcss": "3.4.3", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}