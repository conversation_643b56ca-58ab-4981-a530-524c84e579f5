// 基础样式 - 简化版

// 全局重置
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: inherit;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

// 按钮样式重置
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  font: inherit;
}

// 输入框样式重置
input,
textarea,
select {
  font: inherit;
  color: inherit;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
}

// 列表样式重置
ul,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

// 标题样式重置
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: inherit;
}

// 段落样式重置
p {
  margin: 0;
}

// 表格样式重置
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 表单元素样式重置
fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

legend {
  padding: 0;
}

// 隐藏元素
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}
