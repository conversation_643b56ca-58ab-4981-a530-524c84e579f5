// 常用混入 (Mixins) - 简化版
@use 'sass:map';
@use './variables' as vars;

// 响应式断点
@mixin respond-to($breakpoint) {
  @if map.has-key(vars.$breakpoints, $breakpoint) {
    @media (min-width: map.get(vars.$breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 移动端优先的响应式
@mixin mobile-first($breakpoint) {
  @media (max-width: #{map.get(vars.$breakpoints, $breakpoint) - 1px}) {
    @content;
  }
}

// Flexbox 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flexbox 垂直居中
@mixin flex-center-vertical {
  display: flex;
  align-items: center;
}

// Flexbox 水平居中
@mixin flex-center-horizontal {
  display: flex;
  justify-content: center;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 按钮基础样式
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 卡片基础样式
@mixin card-base {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  padding: 24px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    transform: translateY(-2px);
  }
}

// 文本截断
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本截断
@mixin text-truncate-lines($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 自定义滚动条
@mixin custom-scrollbar($width: 6px, $track-color: #f1f1f1, $thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: 10px;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// 渐变文字
@mixin gradient-text($gradient) {
  background: $gradient;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

// 毛玻璃效果
@mixin glass-effect($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
