/* 动画展示页面样式 */
.animation-showcase {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
}

/* 标题样式 */
.title-gradient {
  font-size: 3.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease-in-out infinite;
  margin-bottom: 1rem;
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
}

/* 动画卡片 */
.animation-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.animation-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.animation-card h3 {
  margin-top: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

/* 脉冲动画 */
.pulse-animation {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-radius: 50%;
  margin: 0 auto;
  animation: pulse-effect 2s ease-in-out infinite;
}

@keyframes pulse-effect {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

/* 旋转动画 */
.spin-animation {
  font-size: 3rem;
  animation: spin-360 2s linear infinite;
  display: inline-block;
}

@keyframes spin-360 {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 弹跳动画 */
.bounce-animation {
  font-size: 3rem;
  animation: bounce-up-down 1s ease-in-out infinite;
  display: inline-block;
}

@keyframes bounce-up-down {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

/* 摇摆动画 */
.swing-animation {
  font-size: 3rem;
  animation: swing-motion 2s ease-in-out infinite;
  transform-origin: top center;
  display: inline-block;
}

@keyframes swing-motion {
  0%, 100% { transform: rotate(0deg); }
  20% { transform: rotate(15deg); }
  40% { transform: rotate(-10deg); }
  60% { transform: rotate(5deg); }
  80% { transform: rotate(-5deg); }
}

/* 呼吸动画 */
.breathe-animation {
  font-size: 3rem;
  animation: breathe-effect 3s ease-in-out infinite;
  display: inline-block;
}

@keyframes breathe-effect {
  0%, 100% { transform: scale(1); filter: brightness(1); }
  50% { transform: scale(1.1); filter: brightness(1.2); }
}

/* 彩虹边框 */
.rainbow-border {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
  background-size: 400% 400%;
  animation: rainbow-rotate 3s linear infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

@keyframes rainbow-rotate {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 波浪动画 */
.wave-animation {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: wave-ripple 2s linear infinite;
}

.wave:nth-child(2) { animation-delay: 0.5s; }
.wave:nth-child(3) { animation-delay: 1s; }

@keyframes wave-ripple {
  0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 0; }
}

/* 加载点动画 */
.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.loading-dots div {
  width: 12px;
  height: 12px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots div:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* 心跳动画 */
.heartbeat-animation {
  font-size: 3rem;
  animation: heartbeat-pulse 1.5s ease-in-out infinite;
  display: inline-block;
}

@keyframes heartbeat-pulse {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

/* 翻转卡片 */
.flip-card {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  perspective: 1000px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
  animation: auto-flip 4s infinite;
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: linear-gradient(45deg, #ff9a9e, #fecfef);
}

.flip-card-back {
  background: linear-gradient(45deg, #a8edea, #fed6e3);
  transform: rotateY(180deg);
}

@keyframes auto-flip {
  0%, 50% { transform: rotateY(0deg); }
  50.01%, 100% { transform: rotateY(180deg); }
}

/* 渐变背景 */
.gradient-animation {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 20px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 粒子效果 */
.particle-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #fff, #4ecdc4);
  border-radius: 50%;
  animation: particle-float 3s linear infinite;
}

.particle:nth-child(odd) { animation-duration: 2s; }
.particle:nth-child(3n) { animation-duration: 4s; }

@keyframes particle-float {
  0% {
    transform: translateY(80px) translateX(0px) scale(0);
    opacity: 1;
  }
  50% {
    transform: translateY(40px) translateX(20px) scale(1);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0px) translateX(0px) scale(0);
    opacity: 0;
  }
}

/* 霓虹灯文字 */
.neon-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #fff;
  text-shadow: 
    0 0 5px #ff6b6b,
    0 0 10px #ff6b6b,
    0 0 15px #ff6b6b,
    0 0 20px #ff6b6b;
  animation: neon-flicker 2s ease-in-out infinite alternate;
}

@keyframes neon-flicker {
  from {
    text-shadow: 
      0 0 5px #ff6b6b,
      0 0 10px #ff6b6b,
      0 0 15px #ff6b6b,
      0 0 20px #ff6b6b;
  }
  to {
    text-shadow: 
      0 0 2px #ff6b6b,
      0 0 5px #ff6b6b,
      0 0 8px #ff6b6b,
      0 0 12px #ff6b6b;
  }
}

/* 液体按钮 */
.liquid-button {
  position: relative;
  padding: 12px 24px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 25px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
}

.liquid-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.liquid-button:hover::before {
  left: 100%;
}

.liquid-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

/* 磁场效果 */
.magnetic-field {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border-radius: 50%;
  border: 2px solid #4ecdc4;
}

.magnetic-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #ff6b6b;
  border-radius: 50%;
  animation: magnetic-orbit 4s linear infinite;
}

.magnetic-dot:nth-child(1) { animation-delay: 0s; }
.magnetic-dot:nth-child(2) { animation-delay: 0.33s; }
.magnetic-dot:nth-child(3) { animation-delay: 0.66s; }
.magnetic-dot:nth-child(4) { animation-delay: 1s; }

@keyframes magnetic-orbit {
  from {
    transform: rotate(0deg) translateX(35px) rotate(0deg);
  }
  to {
    transform: rotate(360deg) translateX(35px) rotate(-360deg);
  }
}

/* 光束扫描 */
.scan-animation {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
  border: 2px solid #4ecdc4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  overflow: hidden;
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, #ff6b6b, transparent);
  animation: scan-sweep 2s linear infinite;
  transform-origin: bottom center;
}

@keyframes scan-sweep {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 星空背景 */
.starry-sky {
  position: relative;
  height: 300px;
  background: linear-gradient(to bottom, #0c0c0c, #1a1a2e, #16213e);
  border-radius: 20px;
  overflow: hidden;
}

.star {
  position: absolute;
  background: white;
  border-radius: 50%;
  animation: twinkle 3s ease-in-out infinite;
}

.star:nth-child(odd) {
  width: 2px;
  height: 2px;
  animation-duration: 2s;
}

.star:nth-child(even) {
  width: 1px;
  height: 1px;
  animation-duration: 4s;
}

.star:nth-child(3n) {
  animation-duration: 1.5s;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.content-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

/* 水波纹效果 */
.ripple-container {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
}

.ripple-text {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  z-index: 2;
  position: relative;
}

.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-expand 1s ease-out;
  pointer-events: none;
}

@keyframes ripple-expand {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .title-gradient {
    font-size: 2.5rem;
  }
  
  .animation-card {
    padding: 1.5rem;
  }
  
  .starry-sky {
    height: 200px;
  }
}