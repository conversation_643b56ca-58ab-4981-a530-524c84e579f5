version: "3.8"

services:
  db:
    image: postgres:13
    container_name: upload-db
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: example
      POSTGRES_DB: upload
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./apps/backend
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: "*************************************/upload?schema=public"
    depends_on:
      db:
        condition: service_healthy

volumes:
  postgres-data:
